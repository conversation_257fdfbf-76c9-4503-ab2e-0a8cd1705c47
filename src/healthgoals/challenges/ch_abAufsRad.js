import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import { exitWithSlide, router } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { setupChallengeResetMenu } from "../../../helpers/reset-helper.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage, getAppData } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgAbAufsRad from "../../../img/healthgoals/challenges/ch_abAufsRad.jpg";
import { initDragScroll, refreshDragScroll } from "../../../helpers/dragScroll.js";
import { showDialog } from "../../../helpers/dialog-helper.js";
import { progressGaugeCard } from "../../../webcomponents/progressGaugeCard.js";
import iconLink from "../../../svg/icons/icon_link.svg";
import iconTagSport from "../../../svg/icons/icon_tag_sport.svg";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.aktivInDerNatur
    ? "/healthgoals-overview/hg-aktivInDerNatur-active"
    : "/healthgoals-overview/hg-aktivInDerNatur";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget),
      menuId: "challenge-ab-aufs-rad-menu"
    })}
  `;
};

/**
 * List Item Content für Trainingsplan
 */
const trainingsplan = [
  {
    text: "1. Training: 10 Min. Radfahren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/ab-aufs-rad")
  },
  {
    text: "2. Training: 10 Min. Radfahren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/ab-aufs-rad")
  },
  {
    text: "3. Training: 10 Min. Radfahren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/ab-aufs-rad")
  },
  {
    text: "4. Training: 10 Min. Radfahren",
    icon: iconChevronRight,
    iconPosition: "right",
    onClick: () => router.navigate("/training/ab-aufs-rad")
  }
];

/**
 * Segmented Control für die Tabs "Übung" und "Trainingsplan"
 */
const segments = [
  {
    id: "uebung",
    title: "Übung",
    content: html`
      <div class="tabpadding black-text">
        <!-- Beschreibungstext -->
        <p class="challenge-description content-padding">
          Lass öfter mal das Auto stehen oder verzichte auf öffentliche Verkehrsmittel und schwing Dich stattdessen aufs Fahrrad! Radfahren ist ein echter Kalorienkiller und bringt Dein Herz-Kreislauf-System in Schwung.
        </p>

        <!-- Grüne Info-Card mit Bullet Points -->
        <div class="standard-container content-padding">
          ${infoCardBullets({
            title: "Was wirst Du erreichen?",
            bulletPoints: [
              "Du reduzierst Dein Herzinfarktrisiko.",
              "Du unterstützt Dein Immunsystem.",
              "Radfahren kann dazu beitragen, dass Du Dich gesünder, vitaler und entspannter fühlst."
            ],
            background: "--info-card-background-green"
          })}
        </div>

        ${sectionTitle("Diese Übungen erwarten Dich")}
        <!-- Image Cards -->
        <div class="image-card-narrow-scroll-container">
          <div class="standard-container content-padding image-card-narrow-container">
            ${imageCardNarrow({
              imgPath: imgAbAufsRad,
              title: "10 Min. Radfahren",
              altText: "10 Min. Radfahren",
              link: "/training/ab-aufs-rad"
            })}
            ${imageCardNarrow({
              imgPath: imgAbAufsRad,
              title: "10 Min. Radfahren",
              altText: "10 Min. Radfahren",
              link: "/training/ab-aufs-rad"
            })}
            ${imageCardNarrow({
              imgPath: imgAbAufsRad,
              title: "10 Min. Radfahren",
              altText: "10 Min. Radfahren",
              link: "/training/ab-aufs-rad"
            })}
            ${imageCardNarrow({
              imgPath: imgAbAufsRad,
              title: "10 Min. Radfahren",
              altText: "10 Min. Radfahren",
              link: "/training/ab-aufs-rad"
            })}
          </div>
        </div>

        <!-- Gelbe Info-Card mit Vorsichtsmaßnahmen -->
        <div class="standard-container content-padding">
          ${infoCardPlain({
            title: "Vorsichtsmaßnahmen",
            text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
            <br><br>
            Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
            <ul>
              <li>bei akuten Erkrankungen</li>
              <li>nach operativen Eingriffen</li>
              <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
            </ul>
            <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      </div>
    `
  },
  {
    id: "trainingsplan",
    title: "Trainingsplan",
    content: html`
      <div class="tabpadding">
        <div class="content-padding content-no-top-padding">
          ${listItem(trainingsplan)}
        </div>
      </div>
    `
  }
];

const segmentedControl = createSegmentedControl(segments);

/**
 * Zeigt den Dialog zur App-Verbindung an
 */
const showConnectionDialog = () => {
  // Setze den Challenge-Kontext für die Tracker-Verbindung
  appStorage.setCurrentChallengeContext('abAufsRad');

  showDialog({
    title: "Verbinde bitte Deine Fitness-App",
    text: unsafeHTML("<p>Wähle mindestens eine der folgenden Verbindungen aus, bevor Du mit dieser Challenge startest.</p> <p>Verbinde Dein Fitness-Armband und starte eine Aktivität.</p> <p>Für die Bonifizierung prüft die NAVIDA-App Deine Trainingsdaten sobald Du den Button \"Training erfassen\" drückst.</p>"),
    icon: iconLink,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Weiter",
      link: "/tracker-connect"
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Zeigt den Dialog zur Trainingserfassung an
 */
const showTrainingRecordDialog = () => {
  showDialog({
    title: "Wann hast Du trainiert?",
    text: unsafeHTML("Toll, dass Du Deine Tageschallenge erfolgreich durchgeführt hast.<br>Nach Deiner Bestätigung wird überprüft, ob Dein Fitness-Tracker etwas aufgezeichnet hat und Du ausreichend trainiert hast. Das kann bis zu zwei Tage dauern."),
    icon: iconTagSport,
    iconColor: "var(--bubble-yellow)",
    iconCorner: "top-left",
    buttonPrimary: {
      text: "Ich habe heute trainiert",
      onClick: () => {
        // Aktuelle Challenge-Daten abrufen
        const currentChallenge = appStorage._data.challenges?.abAufsRad;
        if (!currentChallenge) {
          console.error("Challenge-Daten nicht gefunden");
          return;
        }

        const currentCompletedTrainings = currentChallenge.completedTrainings || 0;
        const totalTrainings = currentChallenge.totalTrainings || 4;

        // Prüfen, ob bereits alle Trainings absolviert wurden
        if (currentCompletedTrainings >= totalTrainings) {
          console.log("Alle Trainings bereits absolviert");
          return;
        }

        // Training erfassen
        const newCompletedTrainings = currentCompletedTrainings + 1;
        appStorage.updateChallenge('abAufsRad', {
          completedTrainings: newCompletedTrainings
        });

        console.log(`Training erfasst: ${newCompletedTrainings} von ${totalTrainings}`);

        // DIREKTE Animation ohne Re-Rendering
        const oldPercentage = Math.round((currentCompletedTrainings / totalTrainings) * 100);
        const newPercentage = Math.round((newCompletedTrainings / totalTrainings) * 100);

        // Aktualisiere die Trainingsanzahl sofort im DOM
        const gaugeTexts = document.querySelectorAll('.gaugeText');
        gaugeTexts.forEach(gaugeText => {
          if (gaugeText && gaugeText.innerHTML.includes('von')) {
            gaugeText.innerHTML = `${newCompletedTrainings} von ${totalTrainings}<br>erledigt`;
          }
        });

        // Starte Animation direkt
        setTimeout(() => {
          scrollToTop();
          setTimeout(() => {
            console.log(`Direkte Animation: ${oldPercentage}% -> ${newPercentage}%`);
            animateProgress(oldPercentage, newPercentage);

            // Nach der Animation prüfen, ob Challenge abgeschlossen ist und UI direkt aktualisieren
            setTimeout(() => {
              if (newCompletedTrainings >= totalTrainings) {
                console.log("Challenge abgeschlossen, aktualisiere UI direkt...");

                // Direkte DOM-Manipulation statt Re-Rendering
                updateUIForCompletedChallenge();

                console.log("Challenge-Abschluss UI-Update abgeschlossen - bleibe auf aktueller Seite");
              }
            }, 2500); // Nach der Animation (2000ms) + etwas Puffer
          }, 800);
        }, 200);
      }
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Scrollt animiert zum oberen Bereich der Seite
 */
const scrollToTop = () => {
  setTimeout(() => {
    const pageContent = document.querySelector(".page-content");
    if (pageContent) {
      console.log("Scrolle nach oben...");
      pageContent.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      console.log("Kein .page-content Container gefunden");
    }
  }, 300);
};

/**
 * Animiert die Fortschrittsanzeige von einem Wert zum anderen
 * @param {number} fromValue - Startwert
 * @param {number} toValue - Zielwert
 * @param {number} duration - Animationsdauer in Millisekunden
 */
const animateProgress = (fromValue, toValue, duration = 2000) => {
  const startTime = performance.now();
  const difference = toValue - fromValue;

  console.log(`Animation gestartet: ${fromValue}% -> ${toValue}%`);

  // Warte bis DOM-Elemente verfügbar sind
  const waitForElements = () => {
    const percentageElement = document.querySelector('.percentage-text');
    const progressArc = document.querySelector('path[stroke-dasharray]');

    if (!percentageElement || !progressArc) {
      console.log('DOM-Elemente noch nicht verfügbar, warte...');
      setTimeout(waitForElements, 50);
      return;
    }

    console.log('DOM-Elemente gefunden, starte Animation...');

    // Starte Animation wenn Elemente verfügbar sind
    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Easing-Funktion für sanfte Animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = Math.round(fromValue + (difference * easeOutQuart));

      // Aktualisiere die Anzeige
      percentageElement.textContent = `${currentValue} %`;

      // Aktualisiere den Arc - KORRIGIERTE Berechnung für centerY = 130, radius = 50
      const arcLength = (270 / 360) * 2 * Math.PI * 50; // 270° von Kreis mit radius 50
      const strokeDashoffset = arcLength * (1 - currentValue / 100);
      progressArc.style.strokeDashoffset = strokeDashoffset;

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        console.log(`Animation beendet bei ${currentValue}%`);
      }
    };

    requestAnimationFrame(animate);
  };

  waitForElements();
};

/**
 * Aktualisiert die UI für eine abgeschlossene Challenge mit CSS Show/Hide
 */
const updateUIForCompletedChallenge = () => {
  console.log("Aktualisiere UI für abgeschlossene Challenge mit CSS Show/Hide...");

  // 1. Pills: Verstecke "Gerade Aktiv", zeige "Abgeschlossen"
  const pillsActive = document.querySelector('.challenge-pills-active');
  const pillsCompleted = document.querySelector('.challenge-pills-completed');

  if (pillsActive) {
    pillsActive.classList.add('challenge-state-hidden');
    console.log("Aktive Pills versteckt");
  }

  if (pillsCompleted) {
    pillsCompleted.classList.remove('challenge-state-hidden');
    console.log("Abgeschlossene Pills angezeigt");
  }

  // 2. Button-Container: Verstecke aktiven Container, zeige abgeschlossenen Container
  const buttonActive = document.querySelector('.challenge-button-active');
  const buttonCompleted = document.querySelector('.challenge-button-completed');

  if (buttonActive) {
    buttonActive.classList.add('challenge-state-hidden');
    console.log("Aktiver Button-Container versteckt");
  }

  if (buttonCompleted) {
    buttonCompleted.classList.remove('challenge-state-hidden');
    console.log("Abgeschlossener Button-Container angezeigt");
  }

  console.log("Challenge-Abschluss UI-Update mit CSS Show/Hide abgeschlossen");
};

/**
 * Template für die "Ab aufs Rad" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templateAbAufsRad = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Ab aufs Rad - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Ab aufs Rad - Healthgoal aktiv:', appStorage._data.activeHealthGoals.aktivInDerNatur);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  // Prüfen, ob die Challenge bereits gestartet wurde
  const appData = getAppData();
  const challengeStarted = appData.challenges &&
                          appData.challenges.abAufsRad &&
                          appData.challenges.abAufsRad.started;

  // Daten für die Challenge
  const completedTrainings = challengeStarted ? appStorage._data.challenges.abAufsRad.completedTrainings : 0;
  const totalTrainings = challengeStarted ? appStorage._data.challenges.abAufsRad.totalTrainings : 4;

  // Prüfen, ob alle Trainings abgeschlossen sind
  const allTrainingsCompleted = challengeStarted && completedTrainings >= totalTrainings;

  // Debug-Ausgaben
  console.log('Challenge Status:', {
    challengeStarted,
    completedTrainings,
    totalTrainings,
    allTrainingsCompleted,
    challengeData: appStorage._data.challenges?.abAufsRad
  });

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <h2 class="content-no-bottom-margin content-padding">
        <span class="dark-grn-text">Ab aufs Rad</span>
      </h2>

      <!-- Pills Container -->
      <div class="content-padding">
        <!-- Pills für nicht gestartete Challenge -->
        <div class="challenge-pills-not-started ${challengeStarted ? 'challenge-state-hidden' : ''}">
          ${pillsContainer([
            { text: "10 Min.", color: "--pill-green-background", textColor: "--pill-green-text" },
            { text: "14 Tage", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconTimelater, iconPosition: "left" },
            { text: "auch ohne Fitness-Armband möglich", color: "--pill-green-background", textColor: "--pill-green-text", iconName: iconInfo, iconPosition: "right" }
          ])}
        </div>

        <!-- Pills für aktive Challenge -->
        <div class="challenge-pills-active ${!challengeStarted || allTrainingsCompleted ? 'challenge-state-hidden' : ''}">
          ${pillsContainer([
            { text: "Gerade Aktiv", color: "--pill-accent-blue-background", textColor: "--pill-accent-blue-text" }
          ])}
        </div>

        <!-- Pills für abgeschlossene Challenge -->
        <div class="challenge-pills-completed ${!challengeStarted || !allTrainingsCompleted ? 'challenge-state-hidden' : ''}">
          ${pillsContainer([
            { text: "Abgeschlossen", color: "--pill-green-background", textColor: "--pill-green-text" }
          ])}
        </div>
      </div>

      <!-- Progress Gauge Card - nur anzeigen, wenn die Challenge gestartet wurde -->
      ${challengeStarted ? html`
        <div class="content-padding">
          ${progressGaugeCard({
            title: "Das steht als nächstes an:",
            description: "1. Training: 10 Min. Radfahren",
            completedTrainings: completedTrainings,
            totalTrainings: totalTrainings
          })}
        </div>
      ` : ''}

      <!-- Segmented Control (Tab Bar) -->
      <div id="segmentedControlContainer" class="standard-container">
        ${segmentedControl.template}
      </div>

      <!-- Erfahre mehr Text -->
      <p class="content-padding black-text">Erfahre hier mehr.</p>

      <!-- Info Button -->
      <div class="standard-container content-padding">
        ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
      </div>

      <!-- Start/Erfassen Button - nur anzeigen wenn Healthgoal aktiv, Niveau bestimmt und keine andere Challenge aktiv -->
      ${(() => {
        const hasActiveChallenge = appStorage.hasActiveChallenge();
        const isThisChallengeActive = appStorage.isChallengeActive('abAufsRad');
        const healthGoalLevel = appStorage.getHealthGoalLevelForGoal('aktivInDerNatur') || appStorage._data.healthGoalLevel;
        const canShowButton = appStorage._data.activeHealthGoals.aktivInDerNatur &&
                             healthGoalLevel &&
                             (!hasActiveChallenge || isThisChallengeActive);
        return canShowButton;
      })() ? html`
        <!-- Challenge nicht gestartet -->
        <div class="challenge-button-not-started standard-container content-padding ${challengeStarted ? 'challenge-state-hidden' : ''}">
          ${buttonStandard({
            text: "Challenge jetzt starten",
            variant: "primary",
            onClick: showConnectionDialog
          })}
        </div>

        <!-- Challenge aktiv (nicht abgeschlossen) -->
        <div class="challenge-button-active standard-container content-padding training-done-container ${!challengeStarted || allTrainingsCompleted ? 'challenge-state-hidden' : ''}">
          <p class="content-top-padding caption center-text">
            Bestätige mir hier, dass Du trainiert hast, damit Deine Aktivität überprüft werden kann.
          </p>
          ${buttonStandard({
            text: "Training erfassen",
            variant: "primary",
            onClick: showTrainingRecordDialog
          })}
        </div>

        <!-- Challenge abgeschlossen -->
        <div class="challenge-button-completed standard-container content-padding training-done-container-inactive ${!challengeStarted || !allTrainingsCompleted ? 'challenge-state-hidden' : ''}">
          <p class="content-top-padding caption center-text">
            Du hast Dein Training für heute bereits erfasst. Morgen kannst Du die nächste Tageschallenge absolvieren und hier erfassen.
          </p>
        </div>
      ` : html`
        <!-- Button ausgeblendet: Bedingungen nicht erfüllt -->
        <div class="standard-container content-padding">
          <p class="caption black-text">
            ${!appStorage._data.activeHealthGoals.aktivInDerNatur
              ? "Aktiviere zuerst das Gesundheitsziel, um Challenges zu starten."
              : !healthGoalLevel
                ? "Bestimme zuerst Dein Healthgoal-Niveau, um Challenges zu starten."
                : appStorage.hasActiveChallenge() && !appStorage.isChallengeActive('abAufsRad')
                  ? "Du hast bereits eine andere Challenge gestartet. Schließe diese zuerst ab oder setze sie zurück."
                  : "Challenge kann nicht gestartet werden."
            }
          </p>
        </div>
      `}
    </div>
    <style>
      .listItemText {
        cursor: pointer;
      }
    </style>
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Challenge
 * Sollte nach dem Rendern der Seite aufgerufen werden
 */
export const initializeAbAufsRadResetMenu = () => {
  setTimeout(() => {
    setupChallengeResetMenu(
      "challenge-ab-aufs-rad-menu",
      "abAufsRad",
      "Ab aufs Rad",
      () => {
        // Callback nach Reset - zurück zur Health Goal Seite
        console.log("Challenge Ab aufs Rad wurde zurückgesetzt, navigiere zurück...");
        const backTarget = appStorage._data.activeHealthGoals.aktivInDerNatur
          ? "/healthgoals-overview/hg-aktivInDerNatur-active"
          : "/healthgoals-overview/hg-aktivInDerNatur";
        router.navigate(backTarget);
      }
    );
  }, 100);
};

console.log("Challenge Ab aufs Rad loaded");
