import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconInfo from "../../../svg/icons/icon_info.svg";
import iconTimelater from "../../../svg/icons/icon_timelater.svg";
import { exitWithSlide, router } from "../../../router.js";
import { buttonTextIcon } from "../../../webcomponents/buttonTextIcon.js";
import { setupChallengeResetMenu } from "../../../helpers/reset-helper.js";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { infoCardBullets } from "../../../webcomponents/infoCardBullets.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { createSegmentedControl } from "../../../webcomponents/segmentedControl.js";
import { pillsContainer } from "../../../webcomponents/pills.js";
import iconChevronRight from "../../../svg/icons/icon_chevron_right.svg";
import { listItem } from "../../../webcomponents/listItem.js";
import { appStorage, getAppData } from "../../../utils.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { imageCardNarrow } from "../../../webcomponents/imageCardNarrow.js";
import imgLaufChallenge from "../../../img/healthgoals/challenges/ch_laufChallenge.jpg";
import { refreshDragScroll } from "../../../helpers/dragScroll.js";
import { showDialog } from "../../../helpers/dialog-helper.js";
import { progressGaugeCard } from "../../../webcomponents/progressGaugeCard.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Bestimme das Zurück-Ziel basierend auf dem aktiven Status des Gesundheitsziels
  const backTarget = appStorage._data.activeHealthGoals.aktivInDerNatur
    ? "/healthgoals-overview/hg-aktivInDerNatur-active"
    : "/healthgoals-overview/hg-aktivInDerNatur";

  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget),
      menuId: "challenge-lauf-challenge-menu"
    })}
  `;
};

/**
 * Funktion zum Scrollen nach oben mit Animation
 */
const scrollToTop = () => {
  const pageContent = document.querySelector('.page-content');
  if (pageContent) {
    pageContent.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
};

/**
 * Funktion zur Animation des Progress Gauge
 */
const animateProgress = (oldPercentage, newPercentage) => {
  const progressGauge = document.querySelector('.progress-gauge-card .progress-circle');
  if (progressGauge) {
    console.log(`Animiere Progress von ${oldPercentage}% zu ${newPercentage}%`);
    
    // CSS-Variable für die Animation setzen
    progressGauge.style.setProperty('--progress-start', oldPercentage);
    progressGauge.style.setProperty('--progress-end', newPercentage);
    
    // Animation-Klasse hinzufügen
    progressGauge.classList.add('animate-progress');
    
    // Nach der Animation die Klasse wieder entfernen
    setTimeout(() => {
      progressGauge.classList.remove('animate-progress');
    }, 2000);
  }
};

/**
 * Funktion zur direkten UI-Aktualisierung bei Challenge-Abschluss
 */
const updateUIForCompletedChallenge = () => {
  // Button zu Info-Text ändern
  const buttonContainer = document.querySelector('.challenge-button-started');
  const infoContainer = document.querySelector('.challenge-info-completed');
  
  if (buttonContainer && infoContainer) {
    buttonContainer.classList.add('challenge-state-hidden');
    infoContainer.classList.remove('challenge-state-hidden');
  }

  // Pill zu "Abgeschlossen" ändern
  const pillsContainer = document.querySelector('.pills-container');
  if (pillsContainer) {
    pillsContainer.innerHTML = `
      <div class="pill done-pill">
        <span class="pill-text">Abgeschlossen</span>
      </div>
    `;
  }
};

/**
 * Zeigt den Training-Abschluss-Dialog
 */
const showTrainingCompletionDialog = () => {
  const appData = getAppData();
  const challengeData = appData.challenges?.laufChallenge;
  
  if (!challengeData) return;

  const oldCompletedTrainings = challengeData.completedTrainings || 0;
  const totalTrainings = challengeData.totalTrainings || 4;
  const newCompletedTrainings = Math.min(oldCompletedTrainings + 1, totalTrainings);
  
  const oldPercentage = Math.round((oldCompletedTrainings / totalTrainings) * 100);
  const newPercentage = Math.round((newCompletedTrainings / totalTrainings) * 100);

  console.log(`Training abgeschlossen: ${oldCompletedTrainings} -> ${newCompletedTrainings} von ${totalTrainings}`);

  showDialog({
    title: "Training abgeschlossen!",
    message: `Du hast ${newCompletedTrainings} von ${totalTrainings} Trainings abgeschlossen.`,
    buttonPrimary: {
      text: "Erfassen",
      action: () => {
        console.log("Erfassen-Button geklickt, aktualisiere AppStorage...");
        
        // AppStorage aktualisieren
        appStorage.updateChallengeProgress('laufChallenge', newCompletedTrainings, totalTrainings);
        
        console.log("AppStorage aktualisiert, starte Animation...");

        // Starte Animation direkt
        setTimeout(() => {
          scrollToTop();
          setTimeout(() => {
            console.log(`Direkte Animation: ${oldPercentage}% -> ${newPercentage}%`);
            animateProgress(oldPercentage, newPercentage);

            // Nach der Animation prüfen, ob Challenge abgeschlossen ist und UI direkt aktualisieren
            setTimeout(() => {
              if (newCompletedTrainings >= totalTrainings) {
                console.log("Challenge abgeschlossen, aktualisiere UI direkt...");

                // Direkte DOM-Manipulation statt Re-Rendering
                updateUIForCompletedChallenge();

                console.log("Challenge-Abschluss UI-Update abgeschlossen - bleibe auf aktueller Seite");
              }
            }, 2500); // Nach der Animation (2000ms) + etwas Puffer
          }, 800);
        }, 200);
      }
    },
    buttonSecondary: {
      text: "Abbrechen"
    }
  });
};

/**
 * Template für die "Lauf-Challenge" Challenge-Seite
 * @returns {TemplateResult} Das Challenge-Seiten Template
 */
export const templateLaufChallenge = () => {
  // Healthgoal-Level für Debugging ausgeben
  console.log('Challenge Lauf-Challenge - Healthgoal-Level:', appStorage._data.healthGoalLevel);
  console.log('Challenge Lauf-Challenge - Healthgoal aktiv:', appStorage._data.activeHealthGoals.aktivInDerNatur);

  // Nach dem Rendern das Drag-Scrolling initialisieren
  setTimeout(() => {
    refreshDragScroll('.image-card-narrow-container');
  }, 100);

  // Prüfen, ob die Challenge bereits gestartet wurde
  const appData = getAppData();
  const challengeStarted = appData.challenges &&
                          appData.challenges.laufChallenge &&
                          appData.challenges.laufChallenge.started;

  // Daten für die Challenge
  const completedTrainings = challengeStarted ? appStorage._data.challenges.laufChallenge.completedTrainings : 0;
  const totalTrainings = challengeStarted ? appStorage._data.challenges.laufChallenge.totalTrainings : 4;

  // Prüfen, ob alle Trainings abgeschlossen sind
  const allTrainingsCompleted = challengeStarted && completedTrainings >= totalTrainings;

  // Debug-Ausgaben
  console.log('Challenge Status:', {
    challengeStarted,
    completedTrainings,
    totalTrainings,
    allTrainingsCompleted,
    challengeData: appStorage._data.challenges?.laufChallenge
  });

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        <img src="${imgLaufChallenge}" alt="Lauf-Challenge" class="challenge-hero-image">
        
        <!-- Pills Container für Status -->
        <div class="pills-container content-top-padding">
          ${challengeStarted ? (
            allTrainingsCompleted ? html`
              <div class="pill done-pill">
                <span class="pill-text">Abgeschlossen</span>
              </div>
            ` : html`
              <div class="pill active-pill">
                <span class="pill-text">Gerade aktiv</span>
              </div>
            `
          ) : ''}
        </div>

        <h1 class="challenge-title">Lauf-Challenge</h1>
        <p class="challenge-subtitle">Laufen ist der Klassiker unter den Ausdauersportarten. Laufen hilft Dir nicht nur dabei, Stress abzubauen, sondern kann zudem Deine Fettverbrennung ankurbeln und Deinen Cholesterinspiegel senken.</p>
        
        <!-- Challenge Info -->
        <div class="challenge-info-grid content-top-padding">
          <div class="challenge-info-item">
            <img src="${iconTimelater}" alt="Zeit" class="challenge-info-icon">
            <span class="challenge-info-text">20 Min. pro Training</span>
          </div>
          <div class="challenge-info-item">
            <img src="${iconInfo}" alt="Intervall" class="challenge-info-icon">
            <span class="challenge-info-text">14 Tage, 2 Mal pro Woche</span>
          </div>
        </div>

        <!-- Progress Gauge - nur anzeigen wenn Challenge gestartet -->
        ${challengeStarted ? html`
          <div class="content-top-padding">
            ${progressGaugeCard({
              completedTrainings,
              totalTrainings,
              challengeName: "Lauf-Challenge"
            })}
          </div>
        ` : ''}
      </div>
    </div>

    <!-- Segmented Control -->
    <div class="content-padding">
      ${createSegmentedControl([
        { id: "info", label: "Info", active: true },
        { id: "trainings", label: "Trainings", active: false }
      ])}
    </div>

    <!-- Info Tab Content -->
    <div id="info-content" class="tab-content active">
      <div class="content-padding standard-container">
        ${infoCardBullets({
          title: "Was wirst Du erreichen?",
          bullets: [
            "Deine Herzmuskulatur wird gestärkt.",
            "Deine Sauerstoffaufnahme wird verbessert; das Lungenvolumen nimmt zu.",
            "Deine Fettverbrennung wird angekurbelt.",
            "Du verringerst Dein Thromboserisiko.",
            "Dein Cholesterin- und Blutzuckerspiegel werden gesenkt.",
            "Die Ausschüttung der Stresshormone Adrenalin und Cortisol wird verringert."
          ],
          backgroundColor: "var(--info-card-background-green)"
        })}
      </div>

      <div class="content-padding standard-container content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
      </div>
    </div>

    <!-- Trainings Tab Content -->
    <div id="trainings-content" class="tab-content">
      <div class="content-padding">
        ${sectionTitle("Trainings")}
        
        <div class="image-card-narrow-container">
          ${imageCardNarrow({
            imgPath: imgLaufChallenge,
            title: "Laufen",
            link: "/training/lauf-challenge"
          })}
        </div>
      </div>

      <!-- Start/Erfassen Button - nur anzeigen wenn Healthgoal aktiv, Niveau bestimmt und keine andere Challenge aktiv -->
      ${(() => {
        const hasActiveChallenge = appStorage.hasActiveChallenge();
        const isThisChallengeActive = appStorage.isChallengeActive('laufChallenge');
        const healthGoalLevel = appStorage.getHealthGoalLevelForGoal('aktivInDerNatur') || appStorage._data.healthGoalLevel;
        const canShowButton = appStorage._data.activeHealthGoals.aktivInDerNatur &&
                             healthGoalLevel &&
                             (!hasActiveChallenge || isThisChallengeActive);
        return canShowButton;
      })() ? html`
        <!-- Challenge nicht gestartet -->
        <div class="challenge-button-not-started standard-container content-padding ${challengeStarted ? 'challenge-state-hidden' : ''}">
          ${buttonStandard({
            text: "Challenge starten",
            variant: "primary",
            onClick: () => {
              console.log("Challenge starten geklickt");
              appStorage.startChallenge('laufChallenge', 4); // 4 Trainings total
              router.navigate('/challenge/lauf-challenge'); // Seite neu laden
            }
          })}
        </div>

        <!-- Challenge gestartet aber nicht abgeschlossen -->
        <div class="challenge-button-started standard-container content-padding ${!challengeStarted || allTrainingsCompleted ? 'challenge-state-hidden' : ''}">
          ${buttonStandard({
            text: "Training erfassen",
            variant: "primary",
            onClick: showTrainingCompletionDialog
          })}
        </div>

        <!-- Challenge abgeschlossen -->
        <div class="challenge-info-completed standard-container content-padding ${!allTrainingsCompleted ? 'challenge-state-hidden' : ''}">
          ${infoCardPlain({
            text: "Du hast alle Trainings dieser Challenge abgeschlossen! 🎉",
            backgroundColor: "var(--info-card-background-green)"
          })}
        </div>
      ` : html`
        <!-- Healthgoal nicht aktiv oder andere Challenge aktiv -->
        <div class="standard-container content-padding">
          ${infoCardPlain({
            text: appStorage._data.activeHealthGoals.aktivInDerNatur 
              ? "Du hast bereits eine andere Challenge aktiv. Schließe diese zuerst ab, bevor Du eine neue startest."
              : "Aktiviere zuerst das Gesundheitsziel 'Aktiv in der Natur', um diese Challenge zu starten.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
      `}
    </div>
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Challenge
 * Sollte nach dem Rendern der Seite aufgerufen werden
 */
export const initializeLaufChallengeResetMenu = () => {
  setTimeout(() => {
    setupChallengeResetMenu(
      "challenge-lauf-challenge-menu",
      "laufChallenge",
      "Lauf-Challenge",
      () => {
        // Callback nach Reset - zurück zur Health Goal Seite
        console.log("Challenge Lauf-Challenge wurde zurückgesetzt, navigiere zurück...");
        const backTarget = appStorage._data.activeHealthGoals.aktivInDerNatur
          ? "/healthgoals-overview/hg-aktivInDerNatur-active"
          : "/healthgoals-overview/hg-aktivInDerNatur";
        router.navigate(backTarget);
      }
    );
  }, 100);
};

console.log("Challenge Lauf-Challenge loaded");
