import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconIlluTrackerHeart from "../../../svg/icons/icon_illu_trackerheart.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgGassiGehen2 from "../../../img/healthgoals/challenges/ch_gassiGehen2.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/gassigehen-tierheim"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Gassigehen" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateGassiGehen2Training = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.aktivInDerNatur ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Gassigehen", "", "h1")}
        ${sectionSubtitle("20 Min. pro Training", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconIlluTrackerHeart,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        <style>
          .info-card-plain .icon-container img {
            filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%) hue-rotate(116deg) brightness(90%) contrast(104%);
          }
        </style>
      </div>
    </div>
    <img src="${imgGassiGehen2}" alt="Training Gassigehen">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Du möchtest Dich im Freien bewegen, dabei etwas Sinnvolles tun und würdest Dich über Gesellschaft freuen? Wie wäre es mit einem Ehrenamt bei einem Tierheim in Deiner Nähe? Dort warten viele dankbare Fellnasen, die sich über lange Spaziergänge freuen werden."
      )}
      ${sectionParagraph(
        "In der Regel erhältst Du vorab durch das Tierheim eine kostenlose \"Gassigänger\"-Schulung."
      )}
      ${sectionParagraph(
        "Die Mitarbeitenden in den Tierheimen sind stets bemüht, den Tieren ihren Aufenthalt so angenehm wie möglich zu gestalten. Dazu gehören neben der Bereitstellung von gutem Futter, Wasser, einem schönen Dach über dem Kopf und der Möglichkeit, mit anderen Hunden zu spielen, auch regelmäßige Spaziergänge."
      )}
      ${sectionParagraph(
        "Doch nicht alle Tierheime verfügen über die nötigen Ressourcen, die Hunde täglich Gassi zu führen. Auch, wenn Du Dir vielleicht keinen Hund anschaffen kannst, ist schon Dein Einsatz als \"Hunde-Ausführer\" eine absolute Bereicherung:"
      )}
      ${sectionParagraph(
        "Die Hunde erhalten Streicheleinheiten, sehen etwas von der Welt, bleiben im Kontakt mit Menschen - was wiederum ihre Vermittlungschancen erhöht - und gleichzeitig tust Du etwas für Dein Herz-Kreislauf-System. Win-Win-Situation für alle!"
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Kontaktiere ein Tierheim in Deiner Nähe",
        "Absolviere die Gassigänger-Schulung",
        "Plane 20 Minuten für Deinen Spaziergang ein",
        "Überprüfe das Wetter und wähle passende Kleidung",
        "Informiere Dich über den Charakter des Hundes"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Bequeme Gehkleidung je nach Jahreszeit",
        "Feste, rutschfeste Schuhe",
        "Leine und Halsband (meist vom Tierheim gestellt)",
        "Kotbeutel",
        "Wasser für Dich und den Hund",
        "Optional: Leckerlis (nach Absprache mit dem Tierheim)"
      ])}

      ${sectionParagraph("Tipps für das Training", true)}
      ${sectionBulletList([
        "Beginne mit einem ruhigen Kennenlernen des Hundes",
        "Halte eine entspannte Gehgeschwindigkeit",
        "Lass dem Hund Zeit zum Schnüffeln und Erkunden",
        "Achte auf die Körpersprache des Hundes",
        "Bleibe geduldig und ruhig",
        "Entsorge Hundekot ordnungsgemäß"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training Gassigehen (Tierheim-Hund) loaded");
