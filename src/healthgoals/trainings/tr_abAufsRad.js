import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconSmartphoneTracker from "../../../svg/icons/icon_smartphoneTracker.svg";
import iconIlluTrackerHeart from "../../../svg/icons/icon_illu_trackerheart.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgAbAufsRad from "../../../img/healthgoals/challenges/ch_abAufsRad.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/ab-aufs-rad"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "10 Min. Radfahren" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateAbAufsRadTraining = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.aktivInDerNatur ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));

  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("10 Min. Radfahren", "", "h1")}
        ${sectionSubtitle("(zur Orientierung 15-20 km/h)", "dark-grn-text", true)}
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconIlluTrackerHeart,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        <style>
          .info-card-plain .icon-container img {
            filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%) hue-rotate(116deg) brightness(90%) contrast(104%);
          }
        </style>
      </div>
    </div>
    <img src="${imgAbAufsRad}" alt="Training 10 Min. Radfahren">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Lass öfter mal das Auto stehen oder verzichte auf öffentliche Verkehrsmittel und schwing Dich stattdessen aufs Fahrrad! Radfahren ist ein echter Kalorienkiller und bringt Dein Herz-Kreislauf-System in Schwung."
      )}
      ${sectionParagraph(
        "Achte beim Radeln darauf, einen eher leichten Gang einzulegen und kontinuierlich zu treten. Das ist effektiver für Deine Fitness und schont die Gelenke."
      )}
      ${sectionParagraph(
        "Vermeide hingegen, mit schweren Gängen Geschwindigkeit aufzubauen, wodurch Du dann potentiell dazu neigen könntest, ungleichmäßiger in die Pedale zu treten."
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Wähle eine Route, die zu Deiner Fitness passt (ca. 15-20 km/h Geschwindigkeit)",
        "Plane 10 Minuten für Dein Training ein",
        "Überprüfe das Wetter und wähle passende Kleidung",
        "Kontrolliere Dein Fahrrad vor der Fahrt (Bremsen, Reifen, Kette)",
        "Nimm eine Wasserflasche mit"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Verkehrstaugliches Fahrrad",
        "Fahrradhelm (Pflicht!)",
        "Bequeme Sportkleidung je nach Jahreszeit",
        "Feste Schuhe mit guter Sohle",
        "Reflektierende Kleidung bei schlechten Lichtverhältnissen",
        "Wasser zum Trinken"
      ])}

      ${sectionParagraph("Tipps für das Training", true)}
      ${sectionBulletList([
        "Beginne mit einem leichten Aufwärmen",
        "Wähle einen leichten Gang und trete kontinuierlich",
        "Halte eine gleichmäßige Geschwindigkeit von ca. 15-20 km/h",
        "Achte auf eine aufrechte Körperhaltung",
        "Mache bei Bedarf kurze Pausen",
        "Beachte die Verkehrsregeln und fahre defensiv"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training 10 Min. Radfahren (Ab aufs Rad) loaded");
