import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconIlluTrackerHeart from "../../../svg/icons/icon_illu_trackerheart.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgNordicWalking from "../../../img/healthgoals/challenges/ch_nordicWalking.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/nordic-walking"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Nordic Walking" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateNordicWalkingTraining = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.aktivInDerNatur ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Nordic Walking", "", "h1")}
        ${sectionSubtitle("20 Min. pro Training", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconIlluTrackerHeart,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        <style>
          .info-card-plain .icon-container img {
            filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%) hue-rotate(116deg) brightness(90%) contrast(104%);
          }
        </style>
      </div>
    </div>
    <img src="${imgNordicWalking}" alt="Training Nordic Walking">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Nordic Walking ist ein schonendes und effektives Ganzkörpertraining. Nordic Walking hat sich zu einem beliebte Ausdauersport gemausert und beansprucht etwa 90 Prozent der rund 700 Muskeln unseres Körpers."
      )}

      ${sectionParagraph("Warm-up:", true)}
      ${sectionParagraph(
        "Starte nicht gleich von 0 auf 100, sondern wärme dich vor dem Training auf. Walke zunächst 10 Minuten in langsamem Tempo und mit lockerer Arm- und Stockarbeit."
      )}

      ${sectionParagraph("Cool-down:", true)}
      ${sectionParagraph(
        "Vermindere für einige Minuten die Belastung und leite somit die Erholung ein. Die Dehnung der beanspruchten Muskulatur kann Dein Training abschließen."
      )}

      ${sectionParagraph("Diese Ausrüstung brauchst Du:", true)}
      ${sectionParagraph("Nordic-Walking-Stöcke", true)}
      ${sectionParagraph(
        "Der wesentliche Ausrüstungsgegenstand beim Nordic Walking sind die Stöcke. Diese sollten unbedingt auf Deine Körpergröße abgestimmt sein. Die richtige Stocklänge ermittelst Du zunächst durch Berechnung mit Hilfe der Formel „Körpergröße in Zentimetern x 0,66". Stelle Dich aufrecht hin und setze die Stöcke senkrecht zum Boden auf, wobei die Oberarme am Körper anliegen. Oberarm und Unterarm sollten jetzt einen Winkel von mehr als 90 Grad bilden. Der Teil, wo die Schlaufe am Stock befestigt ist, sollte sich ungefähr auf Bauchnabelhöhe befinden."
      )}
      ${sectionParagraph(
        "Ein Nordic-Walking-Stock sollte aus Carbon oder Composite (Carbon-Glasfaser-Gemisch) bestehen. Die Vorteile eines Stocks mit einem höheren Carbon-Anteil sind dabei ein leichteres Gewicht und eine bessere Stabilität bei gleichzeitig geringeren Vibrationen."
      )}

      ${sectionParagraph("Die Bewegungsphasen bestehen grob aus:", true)}
      ${sectionParagraph("Stockeinsatz (vorn)", true)}
      ${sectionBulletList([
        "Ein Arm und das jeweils diagonale Bein sind vorn.",
        "Stockeinsatz ungefähr mittig zwischen dem vorderen und hinteren Fuß.",
        "Der Stock zeigt dabei schräg nach hinten.",
        "Die Hand ist locker um den Griff geschlossen."
      ])}

      ${sectionParagraph("Zug- bzw. Schubphase nach hinten", true)}
      ${sectionBulletList([
        "Der Stock wird gegen den Boden gedrückt und aktiv von vorn nach hinten durchgeschoben.",
        "Die Hand wird ab der Hüfte nach hinten geöffnet."
      ])}

      ${sectionParagraph("Abdruck", true)}
      ${sectionBulletList([
        "Der Stock wird bis hinter die Hüfte hinausgeschoben, die Hand ist offen/locker.",
        "Ist der Stock maximal weit nach hinten geschoben, drückt sich der Nordic Walker aktiv über den Stock und die Handschlaufe vom Boden ab."
      ])}

      ${sectionParagraph("Vorschwung", true)}
      ${sectionBulletList([
        "Der Stock wird mit lockerem Arm und lockerer Schulter, nah am Körper, wieder nach vorn geschwungen."
      ])}

      ${sectionParagraph("Tipps für das Training", true)}
      ${sectionBulletList([
        "Beginne immer mit einem Warm-up",
        "Achte auf die richtige Stocklänge",
        "Halte eine gleichmäßige Geschwindigkeit",
        "Nutze die diagonale Bewegung (rechter Arm mit linkem Bein)",
        "Beende das Training mit einem Cool-down und Dehnung",
        "Trinke ausreichend Wasser"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training Nordic Walking loaded");
