import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconIlluTrackerHeart from "../../../svg/icons/icon_illu_trackerheart.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgLaufChallenge from "../../../img/healthgoals/challenges/ch_laufChallenge.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/lauf-challenge"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Laufen" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateLaufChallengeTraining = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.aktivInDerNatur ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Laufen", "", "h1")}
        ${sectionSubtitle("20 Min. pro Training", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconIlluTrackerHeart,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        <style>
          .info-card-plain .icon-container img {
            filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%) hue-rotate(116deg) brightness(90%) contrast(104%);
          }
        </style>
      </div>
    </div>
    <img src="${imgLaufChallenge}" alt="Training Laufen">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Laufen ist der Klassiker unter den Ausdauersportarten. Laufen hilft Dir nicht nur dabei, Stress abzubauen, sondern kann zudem Deine Fettverbrennung ankurbeln und Deinen Cholesterinspiegel senken."
      )}
      ${sectionParagraph(
        "Einen Fehler machen die meisten Läuferinnen und Läufer zu Beginn: Sie joggen zu schnell. Du solltest in einem Tempo laufen, bei dem Du entspannt durch die Nase atmen kannst und Dich mit einem Mitlaufenden locker unterhalten könntest."
      )}
      ${sectionParagraph(
        "Anstatt die Geschwindigkeit zu überwachen, ist ein Intervalltraining der ideale Start für Anfänger und Anfängerinnen: Wechsle immer wieder zwischen Gehen und Laufen. Dabei gilt: Etwa 3 Minuten belasten, 4 Minuten erholen."
      )}
      ${sectionParagraph(
        "Das hast Du Dir anders vorgestellt? Denke daran, dass sich Sehnen, Bänder, Muskeln und das Herz-Kreislauf-System erst an die Belastung gewöhnen müssen. Je langsamer und schonender Du startest, desto schneller wirst Du Erfolge erzielen."
      )}
      ${sectionParagraph(
        "Achte außerdem auf die richtigen Laufschuhe und Funktionskleidung."
      )}

      ${sectionParagraph("Vorbereitung und Planung", true)}
      ${sectionBulletList([
        "Wähle eine Route, die zu Deiner Fitness passt",
        "Plane 20 Minuten für Dein Training ein",
        "Überprüfe das Wetter und wähle passende Kleidung",
        "Beginne mit einem Intervalltraining (3 Min. laufen, 4 Min. gehen)",
        "Nimm eine Wasserflasche mit"
      ])}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Bequeme Laufkleidung je nach Jahreszeit",
        "Gut passende Laufschuhe mit ausreichend Dämpfung",
        "Atmungsaktive Funktionskleidung",
        "Wasser zum Trinken",
        "Optional: Pulsuhr oder Fitness-Tracker",
        "Bei schlechten Lichtverhältnissen: reflektierende Kleidung"
      ])}

      ${sectionParagraph("Tipps für das Training", true)}
      ${sectionBulletList([
        "Beginne mit einem leichten Aufwärmen",
        "Laufe in einem Tempo, bei dem Du Dich noch unterhalten könntest",
        "Wechsle zwischen Laufen und Gehen (Intervalltraining)",
        "Achte auf eine gleichmäßige Atmung durch die Nase",
        "Steigere die Intensität schrittweise",
        "Beende das Training mit einem Cool-down"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training Laufen (Lauf-Challenge) loaded");
