import { html } from "lit-html";
import { unsafeHTML } from "lit-html/directives/unsafe-html.js";
import { topMenu } from "../../../webcomponents/topMenu.js";
import iconArrowLeft from "../../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../../svg/icons/PointsVertical.svg";
import iconIlluTrackerHeart from "../../../svg/icons/icon_illu_trackerheart.svg";
import { router, exitWithSlide } from "../../../router.js";
import { sectionTitle } from "../../../webcomponents/sectionTitle.js";
import { sectionSubtitle } from "../../../webcomponents/sectionSubtitle.js";
import { infoCardPlain } from "../../../webcomponents/infoCardPlain.js";
import { sectionParagraph } from "../../../webcomponents/sectionParagraph.js";
import { sectionBulletList } from "../../../webcomponents/sectionBulletList.js";
import { titleDuoColor } from "../../../webcomponents/titleDuoColor.js";
import imgInlineskating from "../../../img/healthgoals/challenges/ch_inlineSkating.jpg";
import { buttonStandard } from "../../../webcomponents/buttonStandard.js";
import { appStorage } from "../../../utils.js";

/**
 * Top Menu Template mit Slide-Out-Animation beim Zurück-Button
 * @returns {TemplateResult} Das Top-Menu Template
 */
const templateTopMenu = () => {
  // Zurück zur Challenge-Seite
  const backTarget = "/challenge/inlineskating"; 
  
  return html`
    ${topMenu({
      backIcon: iconArrowLeft,
      primaryIcon: "",
      menuIcon: iconDotMenu,
      onBack: () => exitWithSlide(backTarget)
    })}
  `;
};

/**
 * Template für die "Inlineskating" Trainings-Seite
 * @returns {TemplateResult} Das Trainings-Seiten Template
 */
export const templateInlineskatingTraining = () => {
  // Prüfen, ob ein aktives Gesundheitsziel vorhanden ist
  const hasActiveHealthGoal = appStorage._data.activeHealthGoals &&
    (appStorage._data.activeHealthGoals.aktivInDerNatur ||
     Object.values(appStorage._data.activeHealthGoals).some(value => value === true));
  
  return html`
    ${templateTopMenu()}
    <div class="content-left-align content-top-padding">
      <div class="content-padding center-text content-bottom-padding">
        ${titleDuoColor("Inlineskating", "", "h1")}
        ${sectionSubtitle("15 Min. pro Training", "dark-grn-text", true)}        
        <div class="standard-container">
          ${infoCardPlain({
            icon: iconIlluTrackerHeart,
            text: "Bitte starte eine Aktivität auf Deinem Fitness-Armband oder in Deiner Fitness-App, falls es das nicht automatisch macht.",
            backgroundColor: "var(--info-card-background-yellow)"
          })}
        </div>
        <style>
          .info-card-plain .icon-container img {
            filter: brightness(0) saturate(100%) invert(26%) sepia(86%) saturate(608%) hue-rotate(116deg) brightness(90%) contrast(104%);
          }
        </style>
      </div>
    </div>
    <img src="${imgInlineskating}" alt="Training Inlineskating">
    <div class="content-padding standard-container content-top-padding">
      ${sectionSubtitle("Details zur Übung", "dark-grn-text")}
    </div>
      ${sectionParagraph(
        "Du suchst nach einer Alternative zum Laufen? Dann bist Du \"auf Rollen\" bestens aufgehoben! Inlineskating ist ein echter Fatburner, doch dank des Spaßfaktors wird die Anstrengung zur Nebensache."
      )}
      ${sectionParagraph(
        "Mit der richtigen Technik gleiten die Inlineskates eher über die Straßen, als dass sie aufstampfen. So werden Stöße vermieden und die Gelenke geschont. Ein weiterer Vorteil des Bewegungsablaufs ist das Mitschwingen der Arme. Du kannst dadurch leichte Verspannungen im Schulter- und Nackenbereich lösen."
      )}
      ${sectionParagraph(
        "Achte unbedingt auf die richtige Schutzausrüstung:"
      )}

      ${sectionParagraph("Schutzausrüstung", true)}
      ${sectionParagraph("Ein Helm:", true)}
      ${sectionParagraph(
        "Es gibt spezielle Skaterhelme, aber ein Fahrradhelm tut es auch. Wichtig ist, dass er richtig sitzt und den Kopf auch beim Rückwärtsfallen schützt."
      )}
      ${sectionParagraph("Knie- und Ellbogenschoner:", true)}
      ${sectionParagraph(
        "Sollten gut sitzen und nicht verrutschen. Hierfür empfehlen sich Schoner, die wie ein Strumpf über die Knie beziehungsweise die Ellenbogen gestülpt und zusätzlich mit Klett fixiert werden."
      )}
      ${sectionParagraph("Handgelenkschoner:", true)}
      ${sectionParagraph(
        "Ein Bruch des Handgelenks zählt zu den häufigsten Verletzungen beim Inlineskaten. Gut sitzende Schoner verringern die Gefahr und schützen vor Schrammen."
      )}
      ${sectionParagraph("Reflektierende Kleidung:", true)}
      ${sectionParagraph(
        "Als Teilnehmer im Straßenverkehr solltest Du Kleidung tragen, die Dich gut sichtbar macht."
      )}

      ${sectionParagraph("Ausrüstung", true)}
      ${sectionBulletList([
        "Inlineskates in der richtigen Größe",
        "Helm (Skater- oder Fahrradhelm)",
        "Knie- und Ellbogenschoner",
        "Handgelenkschoner",
        "Bequeme, atmungsaktive Sportkleidung",
        "Reflektierende Kleidung bei schlechten Lichtverhältnissen",
        "Wasser zum Trinken"
      ])}

      ${sectionParagraph("Tipps für das Training", true)}
      ${sectionBulletList([
        "Beginne mit einem leichten Aufwärmen",
        "Übe zunächst das Bremsen und Fallen",
        "Halte eine gleichmäßige Geschwindigkeit",
        "Nutze die Arme für das Gleichgewicht",
        "Achte auf eine aufrechte Körperhaltung",
        "Vermeide stark befahrene Straßen",
        "Beende das Training mit einem Cool-down"
      ])}

    <div class="content-padding standard-container content-bottom-padding content-top-padding">
        ${infoCardPlain({
          title: "Vorsichtsmaßnahmen",
          text: unsafeHTML(`Du solltest die Challenge nicht weiter ausführen, wenn während der Übung akute Schmerzen in den Gelenken oder Muskeln auftreten.
          <br><br>
          Darüber hinaus darf die Challenge in folgenden Fällen nicht ausgeführt werden:
          <ul>
            <li>bei akuten Erkrankungen</li>
            <li>nach operativen Eingriffen</li>
            <li>Bei chronischen Erkrankungen halte bitte Rücksprache mit Deinem Arzt/Deiner Ärztin.</li>
          </ul>
          <p><strong>Hinweise:</strong> Schwangere, sowie Menschen die kürzlich entbunden haben, sollten die Challenge nicht durchführen. Bitte halte auch in diesem Fall Rücksprache mit Deinem Arzt/Deiner Ärztin.</p>`),
          textColor: "white",
          backgroundColor: "var(--info-card-background-red)"
        })}
    </div>
  `;
};

console.log("Training Inlineskating loaded");
