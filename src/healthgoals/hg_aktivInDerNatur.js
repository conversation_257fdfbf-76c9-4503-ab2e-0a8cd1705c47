import { html, render } from "lit-html";
import { BottomNavigation } from "../bottomNavigation.js";
import { topMenu } from "../../webcomponents/topMenu.js";
import { imageCard } from "../../webcomponents/imageCard.js";
import imgAktivInDerNatur from "../../img/healthgoals/hg_aktivInDerNatur.jpg";
import iconArrowLeft from "../../svg/icons/ArrowLeft.svg";
import iconDotMenu from "../../svg/icons/PointsVertical.svg";
import iconMoneypig from "../../svg/icons/icon_moneypig.svg";
import iconTime from "../../svg/icons/icon_timelater.svg";
import iconInfo from "../../svg/icons/icon_info.svg";
import iconCalendar from "../../svg/icons/icon_calendar.svg";
import { captionUppercase } from "../../webcomponents/captionUppercase.js";
import { Tag } from "../../webcomponents/tag.js";
import { sectionParagraph } from "../../webcomponents/sectionParagraph.js";
import { infoCardBullets } from "../../webcomponents/infoCardBullets.js";
import { buttonTextIcon } from "../../webcomponents/buttonTextIcon.js";
import { buttonStandard } from "../../webcomponents/buttonStandard.js";
import { titleDuoColor } from "../../webcomponents/titleDuoColor.js";
import { exitWithSlide, router } from "../../router.js";
import { challengeCard } from "../../webcomponents/challengeCard.js";
import { appStorage, isChallengeCompleted } from "../../utils.js";
import { setupHealthGoalResetMenu } from "../../helpers/reset-helper.js";

// Challenge images - using existing images where available
import imgPlogging from "../../img/healthgoals/hg_plogging.jpg";
import imgNordicWalking from "../../img/healthgoals/hg_lockereWanderung.jpg"; // Reusing similar image
import imgLaufChallenge from "../../img/healthgoals/hg_steigereDeineAusdauer.jpg"; // Reusing similar image
import imgGassiGehen from "../../img/healthgoals/hg_gassiGehen.jpg";
import imgInlineskating from "../../img/healthgoals/hg_mehrBewegungImAlltag.jpg"; // Reusing similar image
import imgIntervallLaeufe from "../../img/healthgoals/hg_steigereDeineAusdauer.jpg"; // Reusing similar image
import imgAbAufsRad from "../../img/healthgoals/hg_fahrradTour.jpg";

/* Top Menu */
const templateTopMenu = (isActive = false) => html`
  ${topMenu({
    backIcon: iconArrowLeft,
    primaryIcon: "",
    menuIcon: iconDotMenu,
    onBack: () => exitWithSlide("/healthgoals-overview"),
    onClose: null,
    menuId: isActive ? "health-goal-aktiv-natur-active-menu" : "health-goal-aktiv-natur-menu"
  })}
`;

// Hilfsfunktion für Challenge-Status
const getChallengeStatusPill = (challengeKey) => {
  if (isChallengeCompleted(challengeKey)) {
    return {
      text: "Abgeschlossen",
      type: "done"
    };
  } else if (appStorage.isChallengeActive(challengeKey)) {
    return {
      text: "Gerade aktiv",
      type: "active"
    };
  }
  return null;
};

/**
 * Re-rendert die Health Goal Seite ohne vollständige Navigation
 */
const reRenderHealthGoalAktivInDerNatur = () => {
  // Finde den content-wrapper innerhalb des page-content
  const contentWrapper = document.querySelector('.page-content .app-content-wrapper');
  if (contentWrapper) {
    const isActive = appStorage._data.activeHealthGoals?.aktivInDerNatur;
    render(hg_aktivInDerNaturTemplate(isActive), contentWrapper);
    console.log("Health Goal Aktiv in der Natur UI re-rendered");
  } else {
    console.error("Content wrapper not found for re-render!");
  }
};

// Mache die Re-Render-Funktion global verfügbar
window.reRenderHealthGoalAktivInDerNatur = reRenderHealthGoalAktivInDerNatur;

// Haupttemplate mit Parameter für den Aktivitätsstatus
export const hg_aktivInDerNaturTemplate = (isActive = false) => {
  console.log("hg_aktivInDerNaturTemplate aufgerufen mit isActive:", isActive);
  console.log("appStorage._data.activeHealthGoals:", appStorage._data.activeHealthGoals);
  console.log("appStorage._data.healthGoalLevel:", appStorage._data.healthGoalLevel);

  // Hilfsfunktion für Niveau-Text
  const getLevelDisplayText = (level) => {
    switch(level) {
      case 'beginner': return 'Einsteiger';
      case 'intermediate': return 'Fortgeschritten';
      case 'advanced': return 'Profi';
      default: return null;
    }
  };

  // Hole das Niveau für dieses spezifische Gesundheitsziel
  const healthGoalLevel = appStorage.getHealthGoalLevelForGoal('aktivInDerNatur') || appStorage._data.healthGoalLevel;
  const levelText = getLevelDisplayText(healthGoalLevel);
  const hasLevel = healthGoalLevel !== null;

  return html`
    ${templateTopMenu(isActive)}
    <div class="content-left-align content-padding content-top-padding">
      ${captionUppercase("Gesundheitsziel")}
      <h2 style="margin-bottom: 16px;">
        <span class="dark-grn-text">Aktiv in der Natur</span>
        <span class="contrast-grn-text"></span>
      </h2>
    </div>
    <!-- Main Content -->
    <!-- Image Card - aktives Ziel -->
    <div class="standard-container content-padding">
      ${imageCard({
        cardImage: imgAktivInDerNatur,
        pillText: isActive ? "Gerade aktiv" : "",
        pillColor: isActive ? "--accent-blue" : "",
      })}
      <div class="tags-big">
        ${Tag("--tag-green", iconMoneypig, "1500 Punkte", "--tag-text-green")}
        ${Tag("--tag-green", iconTime, "max. 90 Tage", "--tag-text-green")}
        ${levelText ? Tag("--tag-green", null, levelText, "--tag-text-green") : ''}
      </div>
    </div>
    ${sectionParagraph(
      "Zeit zum Durchatmen! Werde in der Natur aktiv und nutze natürliche Gegebenheiten für diese Trainingseinheiten."
    )}
    <!-- Ziel Kachel -->
    <div class="content-padding standard-container content-bottom-padding">
      ${infoCardBullets({
        icoPath: "",
        title: "Dieses Ziel ist etwas für Dich, um beispielsweise ...",
        bulletPoints: [
          "... Dein Herz-Kreislauf-System zu stärken",
          "... mehr Zeit in der Natur zu verbringen",
          "... Deinen Stoffwechsel anzukurbeln",
          "... Dein Immunsystem zu unterstützen.",
        ],
      })}
    </div>
    <!-- So geht's Kachel -->
    <div class="content-padding standard-container content-bottom-padding">
      ${infoCardBullets({
        icoPath: "",
        title: "So geht's:",
        bulletPoints: [
          "Schaffe mindestens 4 Challenges",
          "Challenges können mehrfach durchgeführt werden",
          "Du hast bis zu 90 Tage Zeit",
          "Die Schwierigkeit der Challenges wird individuell an Dein Leistungsniveau angepasst"
        ],
        background: "--info-card-background-yellow"
      })}
    </div>
    <!-- Paragraph -->
    ${sectionParagraph("Das erwartet Dich:", true)}
    ${sectionParagraph(
      "Du kannst später aus folgenden Challenges auswählen. Die Reihenfolge bestimmst Du dabei selbst. Wiederhole auch gern Challenges, die Dir gefallen haben. Sobald Du mindestens vier Challenges erfolgreich absolviert hast, hast Du Dein Ziel erreicht. Du hast dafür bis zu 90 Tage Zeit. Du kannst es aber auch schneller schaffen."
    )}
    <!-- Challenge Cards -->
    <div class="content-padding standard-container content-top-padding content-bottom-padding gap-items-16">
      <a data-navigate="/challenge/plogging-active">
        ${challengeCard({
          imgPath: imgPlogging,
          title: "Plogging-Challenge",
          infoItems: [
            { icoPath: iconTime, text: "30 Min. pro Training" },
            { icoPath: iconCalendar, text: "14 Tage, 2 Mal pro Woche" },
          ],
          statusPill: getChallengeStatusPill("ploggingActive")
        })}
      </a>
      <a data-navigate="/challenge/nordic-walking">
        ${challengeCard({
          imgPath: imgNordicWalking,
          title: "Nordic Walking",
          infoItems: [
            { icoPath: iconTime, text: "**missing content**" },
            { icoPath: iconCalendar, text: "**missing content**" },
          ],
          statusPill: getChallengeStatusPill("nordicWalking")
        })}
      </a>
      <a data-navigate="/challenge/lauf-challenge">
        ${challengeCard({
          imgPath: imgLaufChallenge,
          title: "Lauf-Challenge",
          infoItems: [
            { icoPath: iconTime, text: "**missing content**" },
            { icoPath: iconCalendar, text: "**missing content**" },
          ],
          statusPill: getChallengeStatusPill("laufChallenge")
        })}
      </a>
      <a data-navigate="/challenge/gassigehen-tierheim">
        ${challengeCard({
          imgPath: imgGassiGehen,
          title: "Gassigehen mit (Tierheim-)Hund",
          infoItems: [
            { icoPath: iconTime, text: "**missing content**" },
            { icoPath: iconCalendar, text: "**missing content**" },
          ],
          statusPill: getChallengeStatusPill("gassigehenTierheim")
        })}
      </a>
      <a data-navigate="/challenge/inlineskating">
        ${challengeCard({
          imgPath: imgInlineskating,
          title: "Inlineskating",
          infoItems: [
            { icoPath: iconTime, text: "**missing content**" },
            { icoPath: iconCalendar, text: "**missing content**" },
          ],
          statusPill: getChallengeStatusPill("inlineskating")
        })}
      </a>
      <a data-navigate="/challenge/intervall-laeufe">
        ${challengeCard({
          imgPath: imgIntervallLaeufe,
          title: "Intervall-Läufe",
          infoItems: [
            { icoPath: iconTime, text: "**missing content**" },
            { icoPath: iconCalendar, text: "**missing content**" },
          ],
          statusPill: getChallengeStatusPill("intervallLaeufe")
        })}
      </a>
      <a data-navigate="/challenge/ab-aufs-rad">
        ${challengeCard({
          imgPath: imgAbAufsRad,
          title: "Ab aufs Rad",
          infoItems: [
            { icoPath: iconTime, text: "**missing content**" },
            { icoPath: iconCalendar, text: "**missing content**" },
          ],
          statusPill: getChallengeStatusPill("abAufsRad")
        })}
      </a>
    </div>
    <!-- Zitat -->
    <div
      class="standard-container content-padding content-no-bottom-margin center-text"
    >
      ${titleDuoColor("&bdquo;Schöpfe", " Kraft aus der Natur.&ldquo;", "h2")}
    </div>
    ${sectionParagraph("Erfahre hier mehr.")}
    <div class="standard-container content-padding content-bottom-padding">
      ${buttonTextIcon("Informationen und Quellen", iconInfo, "left")}
    </div>
    <!-- Weiter Button oder Konflikt-Nachricht -->
    ${(() => {
      // Prüfen, ob ein anderes Gesundheitsziel bereits aktiv ist
      const hasOtherActiveHealthGoal = Object.entries(appStorage._data.activeHealthGoals || {})
        .some(([key, value]) => key !== 'aktivInDerNatur' && value === true);

      // Prüfen, ob dieses Gesundheitsziel bereits aktiv ist
      const isThisGoalActive = appStorage._data.activeHealthGoals?.aktivInDerNatur;

      if (hasOtherActiveHealthGoal) {
        return html`
          <div class="standard-container content-padding">
            <p class="caption black-text">
              Du kannst dieses Gesundheitsziel noch nicht neu starten, da Du aktuell ein anderes Gesundheitsziel aktiv hast. Bitte schließe dieses erst ab oder setze es zurück.
            </p>
          </div>
        `;
      } else if (!hasLevel && !isThisGoalActive) {
        return html`
          <div class="standard-container content-padding">
            ${buttonStandard({
              text: "Zum nächsten Schritt",
              variant: "primary",
              link: "/consent",
              onClick: () => {
                // Setze den Kontext für das Gesundheitsziel
                appStorage.setCurrentHealthGoalContext('aktivInDerNatur');
              }
            })}
          </div>
        `;
      } else {
        return html``;
      }
    })()}
    ${BottomNavigation("home")}
  `;
};

/**
 * Initialisiert die Reset-Menu-Funktionalität für die Health Goal Seite
 * Sollte nach dem Rendern der Seite aufgerufen werden
 * @param {boolean} isActive - Ob das Gesundheitsziel aktiv ist
 */
export const initializeAktivInDerNaturResetMenu = (isActive = false) => {
  // Reset-Menu für beide Zustände einrichten (aktiv und inaktiv)
  setTimeout(() => {
    const menuId = isActive ? "health-goal-aktiv-natur-active-menu" : "health-goal-aktiv-natur-menu";
    setupHealthGoalResetMenu(
      menuId,
      "aktivInDerNatur",
      "Aktiv in der Natur"
    );
  }, 100); // Kurze Verzögerung um sicherzustellen, dass das DOM gerendert ist
};

console.log("HG Aktiv in der Natur loaded");
